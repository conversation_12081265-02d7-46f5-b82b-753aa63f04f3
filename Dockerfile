# VERRNN Docker Container
# Platform: x86_64 (linux/amd64) for macOS compatibility
# Security: Restricted volume mounting, non-root user

FROM --platform=linux/amd64 ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Create non-root user for security
RUN groupadd -r verrnn && useradd -r -g verrnn -m -s /bin/bash verrnn

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    unzip \
    libgmp-dev \
    libmpfr-dev \
    libmpc-dev \
    libblas-dev \
    liblapack-dev \
    gfortran \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip3 install --no-cache-dir \
    numpy \
    scipy \
    matplotlib \
    cdd \
    pysmt \
    z3-solver

# Create application directories
RUN mkdir -p /app/verrnn /app/qhull /app/cplex /app/data
RUN chown -R verrnn:verrnn /app

# Switch to non-root user
USER verrnn
WORKDIR /app

# Set Python path
ENV PYTHONPATH=/app/verrnn:$PYTHONPATH

# Create directory for Qhull installation
RUN mkdir -p /app/qhull/build

# Download and build Qhull from source
RUN cd /app/qhull && \
    wget -q https://github.com/qhull/qhull/archive/refs/tags/2020.2.tar.gz && \
    tar -xzf 2020.2.tar.gz && \
    cd qhull-2020.2 && \
    mkdir build && \
    cd build && \
    cmake .. && \
    make -j$(nproc) && \
    cp qhull /app/qhull/qhull

# Copy project files (will be mounted as volume)
# Note: Project files should be mounted as read-only volume for security

# Expose working directory
VOLUME ["/app/verrnn", "/app/data"]

# Default command
CMD ["/bin/bash"]

#!/bin/bash

# VERRNN Project Setup Script
# This script configures the VERRNN environment within the Docker container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
QHULL_PATH="/app/qhull/qhull"
VERRNN_DIR="/app/verrnn"
DATA_DIR="/app/data"
QHULL_CONFIG_FILE="$VERRNN_DIR/polytope/pnt2hresp.py"

echo -e "${BLUE}=== VERRNN Project Setup Script ===${NC}"
echo -e "${BLUE}This script configures the VERRNN environment.${NC}"
echo ""

# Check if running inside Docker container
if [ ! -f /.dockerenv ]; then
    echo -e "${RED}Error: This script should be run inside the Docker container.${NC}"
    echo -e "${YELLOW}Please run: docker-compose exec verrnn bash${NC}"
    echo -e "${YELLOW}Then execute: ./setup-verrnn.sh${NC}"
    exit 1
fi

# Check if VERRNN directory exists
if [ ! -d "$VERRNN_DIR" ]; then
    echo -e "${RED}Error: VERRNN directory not found at $VERRNN_DIR${NC}"
    echo -e "${YELLOW}Please ensure the project files are mounted correctly.${NC}"
    exit 1
fi

# Check if Qhull executable exists
if [ ! -f "$QHULL_PATH" ]; then
    echo -e "${RED}Error: Qhull executable not found at $QHULL_PATH${NC}"
    echo -e "${YELLOW}The Qhull build may have failed during Docker image creation.${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Found Qhull executable at $QHULL_PATH${NC}"

# Create data directory if it doesn't exist
mkdir -p "$DATA_DIR"
echo -e "${GREEN}✓ Data directory ready at $DATA_DIR${NC}"

# Update Qhull path in the configuration file
echo -e "${BLUE}Updating Qhull path configuration...${NC}"

if [ -f "$QHULL_CONFIG_FILE" ]; then
    # Create a backup of the original file
    cp "$QHULL_CONFIG_FILE" "$DATA_DIR/pnt2hresp.py.backup"
    echo -e "${GREEN}✓ Backup created at $DATA_DIR/pnt2hresp.py.backup${NC}"
    
    # Update the Qhull path
    # Note: Since the source is mounted read-only, we'll create a modified version in the data directory
    sed "s|External_Qhull_Path = '.*'|External_Qhull_Path = '$QHULL_PATH'|g" "$QHULL_CONFIG_FILE" > "$DATA_DIR/pnt2hresp.py"
    
    # Copy the modified file back (this will work if the volume is writable)
    if cp "$DATA_DIR/pnt2hresp.py" "$QHULL_CONFIG_FILE" 2>/dev/null; then
        echo -e "${GREEN}✓ Updated Qhull path in $QHULL_CONFIG_FILE${NC}"
    else
        echo -e "${YELLOW}Warning: Could not update the source file (read-only mount).${NC}"
        echo -e "${YELLOW}You may need to manually update the Qhull path or use a writable mount.${NC}"
        echo -e "${YELLOW}Modified file available at: $DATA_DIR/pnt2hresp.py${NC}"
    fi
else
    echo -e "${RED}Error: Configuration file not found at $QHULL_CONFIG_FILE${NC}"
    exit 1
fi

# Test Python imports
echo -e "${BLUE}Testing Python dependencies...${NC}"

cd "$VERRNN_DIR"

python3 -c "
import sys
import traceback

dependencies = [
    'numpy',
    'scipy', 
    'matplotlib',
    'cdd',
    'pysmt',
    'z3'
]

failed = []

for dep in dependencies:
    try:
        __import__(dep)
        print(f'✓ {dep}')
    except ImportError as e:
        print(f'✗ {dep}: {e}')
        failed.append(dep)

if failed:
    print(f'\\nFailed to import: {failed}')
    sys.exit(1)
else:
    print('\\n✓ All Python dependencies imported successfully')
"

if [ $? -ne 0 ]; then
    echo -e "${RED}Python dependency test failed!${NC}"
    exit 1
fi

# Test CPLEX (if installed)
echo -e "${BLUE}Testing CPLEX availability...${NC}"

python3 -c "
try:
    import cplex
    print('✓ CPLEX is available')
    prob = cplex.Cplex()
    print(f'✓ CPLEX version: {prob.get_version()}')
except ImportError:
    print('⚠ CPLEX not installed yet. Run ./install-cplex.sh to install it.')
except Exception as e:
    print(f'⚠ CPLEX error: {e}')
"

# Test project structure
echo -e "${BLUE}Verifying project structure...${NC}"

required_files=(
    "$VERRNN_DIR/pp_test.py"
    "$VERRNN_DIR/fp_test.py" 
    "$VERRNN_DIR/cegar_test.py"
    "$VERRNN_DIR/pulse_test.py"
    "$VERRNN_DIR/dataload/__init__.py"
    "$VERRNN_DIR/polytope/__init__.py"
    "$VERRNN_DIR/smtbmc/__init__.py"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓ Found $(basename $file)${NC}"
    else
        echo -e "${RED}✗ Missing $file${NC}"
    fi
done

# Test model data
echo -e "${BLUE}Checking model data...${NC}"

if [ -d "/app/N7_L1_r11" ]; then
    if [ -f "/app/N7_L1_r11/models/weights.npz" ]; then
        echo -e "${GREEN}✓ Found neural network model data${NC}"
    else
        echo -e "${YELLOW}⚠ Model weights file not found${NC}"
    fi
else
    echo -e "${YELLOW}⚠ Model directory not found${NC}"
fi

# Create convenience scripts
echo -e "${BLUE}Creating convenience scripts...${NC}"

# Create a script to run tests
cat > "$DATA_DIR/run-tests.sh" << 'EOF'
#!/bin/bash
# VERRNN Test Runner

echo "=== VERRNN Test Runner ==="
echo "Available tests:"
echo "1. Polytope Propagation Test (pp_test.py)"
echo "2. Fixed Point Test (fp_test.py)" 
echo "3. CEGAR Test (cegar_test.py)"
echo "4. Pulse Test (pulse_test.py)"
echo ""

cd /app/verrnn

case "$1" in
    "pp"|"1")
        echo "Running Polytope Propagation Test..."
        python3 pp_test.py
        ;;
    "fp"|"2")
        echo "Running Fixed Point Test..."
        python3 fp_test.py
        ;;
    "cegar"|"3")
        echo "Running CEGAR Test..."
        python3 cegar_test.py
        ;;
    "pulse"|"4")
        echo "Running Pulse Test..."
        python3 pulse_test.py
        ;;
    *)
        echo "Usage: $0 [pp|fp|cegar|pulse]"
        echo "   or: $0 [1|2|3|4]"
        exit 1
        ;;
esac
EOF

chmod +x "$DATA_DIR/run-tests.sh"
echo -e "${GREEN}✓ Created test runner at $DATA_DIR/run-tests.sh${NC}"

echo ""
echo -e "${GREEN}=== VERRNN Setup Complete! ===${NC}"
echo -e "${BLUE}Environment is ready for VERRNN verification tasks.${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo -e "${YELLOW}1. Install CPLEX: ./install-cplex.sh${NC}"
echo -e "${YELLOW}2. Run tests: $DATA_DIR/run-tests.sh [test_name]${NC}"
echo -e "${YELLOW}3. Check logs in: $DATA_DIR/${NC}"
echo ""
echo -e "${BLUE}Available test commands:${NC}"
echo -e "${YELLOW}  python3 pp_test.py    # Polytope propagation${NC}"
echo -e "${YELLOW}  python3 fp_test.py    # Fixed point method${NC}"
echo -e "${YELLOW}  python3 cegar_test.py # CEGAR method${NC}"
echo -e "${YELLOW}  python3 pulse_test.py # Pulse method${NC}"

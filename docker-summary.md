# VERRNN Docker Setup - Quick Reference

## Files Created

### Core Docker Files
- `Dockerfile` - Multi-stage build with x86_64 architecture, security hardening
- `docker-compose.yml` - Secure container orchestration with volume restrictions
- `.dockerignore` - Optimized build context (auto-generated)

### Setup Scripts
- `prepare-docker.sh` - Host environment preparation
- `setup-verrnn.sh` - Container environment configuration  
- `install-cplex.sh` - CPLEX installation with manual intervention support

### Documentation
- `DOCKER_README.md` - Comprehensive setup and usage guide
- `docker-summary.md` - This quick reference

## Security Features Implemented

✅ **Platform Specification**: `--platform linux/amd64` for macOS compatibility  
✅ **Non-root User**: Container runs as `verrnn` user  
✅ **Read-only Mounts**: Source code mounted read-only  
✅ **Volume Restrictions**: Only necessary directories mounted  
✅ **Resource Limits**: Memory (8GB) and CPU (4 cores) limits  
✅ **No Privileged Access**: `no-new-privileges:true`  
✅ **Isolated Network**: Bridge network with controlled access  

## Quick Start Commands

```bash
# 1. Prepare environment
chmod +x prepare-docker.sh && ./prepare-docker.sh

# 2. Build and start
docker-compose build && docker-compose up -d

# 3. Enter container and setup
docker-compose exec verrnn bash
./setup-verrnn.sh

# 4. Install CPLEX (manual interaction required)
./install-cplex.sh

# 5. Run verification tests
python3 pp_test.py    # Polytope propagation
python3 fp_test.py    # Fixed point method
python3 cegar_test.py # CEGAR method
python3 pulse_test.py # Pulse method
```

## Volume Mapping

| Host | Container | Access | Purpose |
|------|-----------|--------|---------|
| `./verrnn` | `/app/verrnn` | RO | Source code |
| `./N7_L1_r11` | `/app/N7_L1_r11` | RO | Model data |
| `./cplex_studio2210.linux_x86_64.bin` | `/app/cplex/cplex_studio2210.linux_x86_64.bin` | RO | CPLEX installer |
| `./docker-data` | `/app/data` | RW | Outputs/logs |
| `verrnn-cplex` | `/app/cplex/install` | RW | CPLEX install |

## Dependencies Included

**System Packages:**
- Python 3.x with development headers
- Build tools (gcc, cmake, make)
- Mathematical libraries (BLAS, LAPACK, GMP, MPFR)

**Python Packages:**
- numpy, scipy, matplotlib
- cdd (computational geometry)
- pysmt (SMT solver interface)  
- z3-solver (Z3 theorem prover)

**Built from Source:**
- Qhull 2020.2 (computational geometry)

**Manual Installation:**
- IBM CPLEX Optimization Studio (academic version)

## CPLEX Installation Notes

⚠️ **Manual Intervention Required**: CPLEX installation requires accepting license terms and selecting components.

**Installation Steps:**
1. Run `./install-cplex.sh` inside container
2. Accept IBM license agreement
3. Confirm installation directory: `/app/cplex/install`
4. Select all components (CPLEX, CP Optimizer, etc.)
5. Script automatically installs Python API

**Requirements:**
- Academic version of CPLEX (promotional version has size limits)
- Interactive terminal session for license acceptance
- ~2GB disk space for installation

## Troubleshooting

**Container Issues:**
```bash
docker-compose logs verrnn        # Check logs
docker-compose restart verrnn     # Restart container
docker-compose down && docker-compose up -d  # Full restart
```

**CPLEX Issues:**
```bash
# Verify installer
docker-compose exec verrnn ls -la /app/cplex/
docker-compose exec verrnn file /app/cplex/cplex_studio2210.linux_x86_64.bin

# Test installation
docker-compose exec verrnn python3 -c "import cplex; print(cplex.Cplex().get_version())"
```

**Python Issues:**
```bash
# Test imports
docker-compose exec verrnn python3 -c "import numpy, scipy, cdd, pysmt, z3"

# Check paths
docker-compose exec verrnn echo $PYTHONPATH
```

## Output Files

All outputs saved to `./docker-data/` on host:
- `*_test.log` - Verification results
- `pnt2hresp.py.backup` - Configuration backup
- `run-tests.sh` - Test runner script

## Cleanup

```bash
# Stop containers
docker-compose down

# Remove everything including CPLEX installation
docker-compose down -v
docker rmi verrnn_verrnn
```

## Security Considerations

✅ **Prevents access to macOS system directories** (`/System`, `/usr`, `/bin`, etc.)  
✅ **Source code protection** via read-only mounts  
✅ **Isolated execution environment** with resource limits  
✅ **Non-privileged container execution**  
✅ **Controlled network access**  

The setup ensures the Docker container cannot modify critical macOS system files while providing full functionality for VERRNN verification tasks.

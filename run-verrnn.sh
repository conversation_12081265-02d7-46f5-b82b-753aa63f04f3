#!/bin/bash

# VERRNN Docker Runner Script
# Simple script to run VERRNN container with proper security settings

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="verrnn"
CONTAINER_NAME="verrnn-container"
DATA_DIR="./docker-data"

echo -e "${BLUE}=== VERRNN Docker Runner ===${NC}"

# Check if image exists
if ! docker image inspect $IMAGE_NAME >/dev/null 2>&1; then
    echo -e "${RED}Error: Docker image '$IMAGE_NAME' not found.${NC}"
    echo -e "${YELLOW}Please build the image first:${NC}"
    echo -e "${YELLOW}  docker build --platform linux/amd64 -t verrnn .${NC}"
    exit 1
fi

# Create data directory if it doesn't exist
mkdir -p "$DATA_DIR"

# Check if container is already running
if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
    echo -e "${YELLOW}Container '$CONTAINER_NAME' is already running.${NC}"
    echo -e "${BLUE}Connecting to existing container...${NC}"
    docker exec -it $CONTAINER_NAME bash
    exit 0
fi

# Remove existing stopped container if it exists
if docker ps -aq -f name=$CONTAINER_NAME | grep -q .; then
    echo -e "${YELLOW}Removing existing stopped container...${NC}"
    docker rm $CONTAINER_NAME
fi

# Check for required files
if [ ! -d "./verrnn" ]; then
    echo -e "${RED}Error: VERRNN source directory './verrnn' not found.${NC}"
    echo -e "${YELLOW}Please ensure you're running this from the project root.${NC}"
    exit 1
fi

if [ ! -f "./cplex_studio2210.linux_x86_64.bin" ]; then
    echo -e "${YELLOW}Warning: CPLEX installer not found.${NC}"
    echo -e "${YELLOW}You can install it later inside the container.${NC}"
    CPLEX_MOUNT=""
else
    echo -e "${GREEN}✓ Found CPLEX installer${NC}"
    # Mount to a writable location so we can make it executable
    CPLEX_MOUNT="-v $(pwd)/cplex_studio2210.linux_x86_64.bin:/tmp/cplex_installer.bin:ro"
fi

# Model data mount (optional)
if [ -d "./N7_L1_r11" ]; then
    echo -e "${GREEN}✓ Found model data directory${NC}"
    MODEL_MOUNT="-v $(pwd)/N7_L1_r11:/app/N7_L1_r11:ro"
else
    echo -e "${YELLOW}Warning: Model data directory './N7_L1_r11' not found.${NC}"
    MODEL_MOUNT=""
fi

echo -e "${BLUE}Starting VERRNN container with security settings...${NC}"

# Run container with security settings
docker run -it \
    --name $CONTAINER_NAME \
    --platform linux/amd64 \
    --user verrnn \
    --security-opt no-new-privileges:true \
    --memory=8g \
    --cpus=4.0 \
    --network bridge \
    -v $(pwd)/verrnn:/app/verrnn:ro \
    -v $(pwd)/$DATA_DIR:/app/data:rw \
    -v $(pwd)/setup-verrnn.sh:/app/setup-verrnn.sh:ro \
    -v $(pwd)/install-cplex.sh:/app/install-cplex.sh:ro \
    $CPLEX_MOUNT \
    $MODEL_MOUNT \
    -w /app/verrnn \
    -e PYTHONPATH=/app/verrnn:/app \
    -e QHULL_PATH=/app/qhull/qhull \
    $IMAGE_NAME \
    bash

echo -e "${GREEN}Container session ended.${NC}"
echo -e "${BLUE}To reconnect to the same container (if still running):${NC}"
echo -e "${YELLOW}  docker exec -it $CONTAINER_NAME bash${NC}"
echo ""
echo -e "${BLUE}To start a fresh container:${NC}"
echo -e "${YELLOW}  docker rm $CONTAINER_NAME && ./run-verrnn.sh${NC}"

# VERRNN Docker Container Setup

This document provides comprehensive instructions for setting up and running the VERRNN (Verification of Recurrent Neural Networks) project in a secure Docker container with x86_64 architecture compatibility for macOS.

## Overview

The Docker setup provides:
- **Platform Compatibility**: x86_64 (linux/amd64) architecture for macOS compatibility
- **Security**: Non-root user, read-only mounts, restricted volume access
- **CPLEX Integration**: Support for IBM CPLEX academic version installation
- **Complete Environment**: All dependencies including Qhull, Python packages, and build tools

## Prerequisites

1. **Docker Desktop for macOS** - Install from [docker.com](https://www.docker.com/products/docker-desktop)
2. **IBM CPLEX Academic License** - Download `cplex_studio2210.linux_x86_64.bin` from IBM Academic Initiative
3. **VERRNN Project Files** - This repository with all source code

## Quick Start

### 1. Prepare Environment

```bash
# Make preparation script executable and run it
chmod +x prepare-docker.sh
./prepare-docker.sh
```

### 2. Build Docker Image

```bash
# Build the container (this may take 10-15 minutes)
docker-compose build
```

### 3. Start Container

```bash
# Start the container in detached mode
docker-compose up -d

# Enter the container
docker-compose exec verrnn bash
```

### 4. Setup VERRNN Environment

```bash
# Inside the container, setup the environment
./setup-verrnn.sh
```

### 5. Install CPLEX

```bash
# Install CPLEX (requires manual interaction)
./install-cplex.sh
```

### 6. Run Tests

```bash
# Run verification tests
python3 pp_test.py    # Polytope propagation
python3 fp_test.py    # Fixed point method
python3 cegar_test.py # CEGAR method
python3 pulse_test.py # Pulse method
```

## Detailed Instructions

### Security Features

The Docker setup implements several security measures:

- **Non-root execution**: Container runs as user `verrnn` (UID/GID managed by Docker)
- **Read-only mounts**: Source code is mounted read-only to prevent accidental modification
- **Restricted volumes**: Only necessary directories are mounted
- **No privileged access**: Container cannot access sensitive host system files
- **Resource limits**: Memory and CPU usage are limited

### Volume Mounts

| Host Path | Container Path | Access | Purpose |
|-----------|----------------|--------|---------|
| `./verrnn` | `/app/verrnn` | Read-only | VERRNN source code |
| `./N7_L1_r11` | `/app/N7_L1_r11` | Read-only | Neural network model |
| `./cplex_studio2210.linux_x86_64.bin` | `/app/cplex/cplex_studio2210.linux_x86_64.bin` | Read-only | CPLEX installer |
| `./docker-data` | `/app/data` | Read-write | Output files and logs |
| `verrnn-cplex` | `/app/cplex/install` | Read-write | CPLEX installation |

### CPLEX Installation Process

The CPLEX installation requires manual interaction due to licensing requirements:

1. **Start Installation**: Run `./install-cplex.sh` inside the container
2. **License Agreement**: Accept the IBM license terms
3. **Installation Directory**: Confirm `/app/cplex/install` as the target
4. **Component Selection**: Select all components (CPLEX, CP Optimizer, etc.)
5. **Python API**: The script automatically installs the Python API after main installation

**Important Notes:**
- The academic version is required (promotional version has problem size limits)
- Installation is persistent in the named Docker volume `verrnn-cplex`
- You only need to install CPLEX once per Docker setup

### Environment Configuration

The setup automatically configures:

- **Python Path**: `/app/verrnn` added to `PYTHONPATH`
- **Qhull Path**: Updated to `/app/qhull/qhull` in project configuration
- **CPLEX Environment**: `CPLEX_STUDIO_DIR` set after installation
- **Dependencies**: All required Python packages pre-installed

### Running Verification Tasks

The VERRNN project includes several verification methods:

#### Polytope Propagation
```bash
python3 pp_test.py
```
Tests reachability analysis using polytope propagation.

#### Fixed Point Method
```bash
python3 fp_test.py
```
Tests invariant-based verification using fixed point computation.

#### CEGAR (Counter-Example Guided Abstraction Refinement)
```bash
python3 cegar_test.py
```
Tests CEGAR-based verification approach.

#### Pulse Method
```bash
python3 pulse_test.py
```
Tests pulse-based verification for specific properties.

### Output Files

All output files are saved to the `docker-data` directory on your host:

- **Log Files**: `*_test.log` files containing verification results
- **Plots**: Generated visualization files (if enabled)
- **Backups**: Configuration file backups
- **Debug Data**: Intermediate computation results

### Troubleshooting

#### Container Won't Start
```bash
# Check Docker status
docker-compose ps

# View container logs
docker-compose logs verrnn

# Restart container
docker-compose restart verrnn
```

#### CPLEX Installation Issues
```bash
# Check if installer is accessible
docker-compose exec verrnn ls -la /app/cplex/

# Verify installer permissions
docker-compose exec verrnn file /app/cplex/cplex_studio2210.linux_x86_64.bin

# Manual installation attempt
docker-compose exec verrnn bash
chmod +x /app/cplex/cplex_studio2210.linux_x86_64.bin
/app/cplex/cplex_studio2210.linux_x86_64.bin -i console
```

#### Python Import Errors
```bash
# Test Python environment
docker-compose exec verrnn python3 -c "import numpy, scipy, cdd, pysmt, z3; print('All imports successful')"

# Check PYTHONPATH
docker-compose exec verrnn echo $PYTHONPATH

# Reinstall dependencies if needed
docker-compose exec verrnn pip3 install --user numpy scipy cdd pysmt z3-solver
```

#### Qhull Path Issues
```bash
# Verify Qhull executable
docker-compose exec verrnn /app/qhull/qhull

# Check configuration
docker-compose exec verrnn grep -n "External_Qhull_Path" /app/verrnn/polytope/pnt2hresp.py
```

### Performance Considerations

- **Memory Usage**: Verification tasks can be memory-intensive. The container is limited to 8GB RAM.
- **CPU Usage**: Limited to 4 CPU cores. Adjust in `docker-compose.yml` if needed.
- **Disk Space**: CPLEX installation requires ~2GB. Ensure sufficient disk space.

### Cleanup

```bash
# Stop and remove containers
docker-compose down

# Remove volumes (WARNING: This deletes CPLEX installation)
docker-compose down -v

# Remove images
docker rmi verrnn_verrnn
```

## Advanced Usage

### Custom Configuration

To modify container settings, edit `docker-compose.yml`:

```yaml
# Increase memory limit
deploy:
  resources:
    limits:
      memory: 16G  # Increase from 8G
```

### Development Mode

For development, you can mount the source as writable:

```yaml
# In docker-compose.yml, change:
volumes:
  - type: bind
    source: ./verrnn
    target: /app/verrnn
    read_only: false  # Allow writes
```

### Multiple Containers

To run multiple verification tasks in parallel:

```bash
# Scale the service
docker-compose up --scale verrnn=3 -d

# Execute commands on specific containers
docker-compose exec --index=1 verrnn python3 pp_test.py
docker-compose exec --index=2 verrnn python3 fp_test.py
```

## Support

For issues specific to the Docker setup, check:
1. Docker Desktop logs
2. Container logs: `docker-compose logs verrnn`
3. Host system resources (memory, disk space)

For VERRNN-specific issues, refer to the main project documentation and the original research paper.

#!/bin/bash

# VERRNN Docker Build Script
# Simple script to build the VERRNN Docker image

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

IMAGE_NAME="verrnn"

echo -e "${BLUE}=== VERRNN Docker Build Script ===${NC}"
echo -e "${BLUE}Building Docker image for VERRNN project...${NC}"
echo ""

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed or not in PATH.${NC}"
    echo -e "${YELLOW}Please install Docker Desktop for macOS.${NC}"
    exit 1
fi

# Check if Dockerfile exists
if [ ! -f "Dockerfile" ]; then
    echo -e "${RED}Error: Dockerfile not found in current directory.${NC}"
    echo -e "${YELLOW}Please ensure you're running this from the project root.${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Docker is available${NC}"
echo -e "${GREEN}✓ Dockerfile found${NC}"

# Build the image
echo -e "${BLUE}Building Docker image '$IMAGE_NAME'...${NC}"
echo -e "${YELLOW}This may take 10-15 minutes for the first build.${NC}"
echo ""

docker build \
    --platform linux/amd64 \
    --tag $IMAGE_NAME \
    --progress=plain \
    .

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}=== Build Successful! ===${NC}"
    echo -e "${GREEN}Docker image '$IMAGE_NAME' created successfully.${NC}"
    echo ""
    echo -e "${BLUE}Image details:${NC}"
    docker image inspect $IMAGE_NAME --format='Size: {{.Size}} bytes'
    docker image inspect $IMAGE_NAME --format='Created: {{.Created}}'
    echo ""
    echo -e "${YELLOW}Next steps:${NC}"
    echo -e "${YELLOW}1. Run the container: ./run-verrnn.sh${NC}"
    echo -e "${YELLOW}2. Setup environment: ./setup-verrnn.sh${NC}"
    echo -e "${YELLOW}3. Install CPLEX: ./install-cplex.sh${NC}"
else
    echo ""
    echo -e "${RED}=== Build Failed! ===${NC}"
    echo -e "${RED}Docker image build failed.${NC}"
    echo -e "${YELLOW}Please check the error messages above.${NC}"
    exit 1
fi

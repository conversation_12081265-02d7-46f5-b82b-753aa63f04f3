#!/bin/bash

# Docker Preparation Script for VERRNN
# This script prepares the host environment for running VERRNN in Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== VERRNN Docker Preparation Script ===${NC}"
echo -e "${BLUE}This script prepares your environment for running VERRNN in Docker.${NC}"
echo ""

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed or not in PATH.${NC}"
    echo -e "${YELLOW}Please install Docker Desktop for macOS.${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Docker is available${NC}"

# Create data directory for container outputs
DATA_DIR="./docker-data"
mkdir -p "$DATA_DIR"
echo -e "${GREEN}✓ Created data directory: $DATA_DIR${NC}"

# Make scripts executable
chmod +x install-cplex.sh
chmod +x setup-verrnn.sh
echo -e "${GREEN}✓ Made scripts executable${NC}"

# Check for CPLEX installer
CPLEX_INSTALLER="./cplex_studio2210.linux_x86_64.bin"
if [ -f "$CPLEX_INSTALLER" ]; then
    echo -e "${GREEN}✓ Found CPLEX installer: $CPLEX_INSTALLER${NC}"
else
    echo -e "${YELLOW}⚠ CPLEX installer not found at: $CPLEX_INSTALLER${NC}"
    echo -e "${YELLOW}  Please ensure you have the academic version installer file.${NC}"
fi

# Check for project directories
if [ -d "./verrnn" ]; then
    echo -e "${GREEN}✓ Found VERRNN source directory${NC}"
else
    echo -e "${RED}✗ VERRNN source directory not found${NC}"
    echo -e "${YELLOW}  Please ensure you're running this from the project root.${NC}"
fi

if [ -d "./N7_L1_r11" ]; then
    echo -e "${GREEN}✓ Found model data directory${NC}"
else
    echo -e "${YELLOW}⚠ Model data directory not found${NC}"
fi

# Create .dockerignore file
cat > .dockerignore << 'EOF'
# Docker ignore file for VERRNN
.git
.gitignore
*.log
*.pyc
__pycache__/
.DS_Store
docker-data/
*.md
.vscode/
.idea/
EOF

echo -e "${GREEN}✓ Created .dockerignore file${NC}"

echo ""
echo -e "${GREEN}=== Preparation Complete! ===${NC}"
echo -e "${BLUE}Your environment is ready for VERRNN Docker setup.${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo -e "${YELLOW}1. Build the Docker image:${NC}"
echo -e "${YELLOW}   docker build --platform linux/amd64 -t verrnn .${NC}"
echo ""
echo -e "${YELLOW}2. Run the container:${NC}"
echo -e "${YELLOW}   ./run-verrnn.sh${NC}"
echo ""
echo -e "${YELLOW}3. Setup the environment (inside container):${NC}"
echo -e "${YELLOW}   ./setup-verrnn.sh${NC}"
echo ""
echo -e "${YELLOW}4. Install CPLEX (requires manual interaction):${NC}"
echo -e "${YELLOW}   ./install-cplex.sh${NC}"
echo ""
echo -e "${BLUE}Security Notes:${NC}"
echo -e "${YELLOW}• Project files are mounted read-only for security${NC}"
echo -e "${YELLOW}• Container runs as non-root user 'verrnn'${NC}"
echo -e "${YELLOW}• Only necessary directories are mounted${NC}"
echo -e "${YELLOW}• Output files will be saved to: $DATA_DIR${NC}"

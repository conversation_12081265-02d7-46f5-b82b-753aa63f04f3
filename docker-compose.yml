version: '3.8'

services:
  verrnn:
    build:
      context: .
      dockerfile: Dockerfile
      platform: linux/amd64
    
    container_name: verrnn-container
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    
    # Volume mounts with security restrictions
    volumes:
      # Mount project source code as read-only for security
      - type: bind
        source: ./verrnn
        target: /app/verrnn
        read_only: true
      
      # Mount model data as read-only
      - type: bind
        source: ./N7_L1_r11
        target: /app/N7_L1_r11
        read_only: true
      
      # Mount CPLEX installer (when available)
      - type: bind
        source: ./cplex_studio2210.linux_x86_64.bin
        target: /app/cplex/cplex_studio2210.linux_x86_64.bin
        read_only: true
      
      # Writable data directory for outputs and logs
      - type: bind
        source: ./docker-data
        target: /app/data
        read_only: false
      
      # Writable directory for CPLEX installation
      - verrnn-cplex:/app/cplex/install
    
    # Environment variables
    environment:
      - PYTHONPATH=/app/verrnn:/app
      - QHULL_PATH=/app/qhull/qhull
      - CPLEX_STUDIO_DIR=/app/cplex/install
    
    # Working directory
    working_dir: /app/verrnn
    
    # Network settings (isolated)
    networks:
      - verrnn-network
    
    # Prevent container from accessing host network
    network_mode: bridge
    
    # User settings (non-root)
    user: verrnn
    
    # Interactive mode for CPLEX installation
    stdin_open: true
    tty: true

# Named volumes for persistent data
volumes:
  verrnn-cplex:
    driver: local

# Isolated network
networks:
  verrnn-network:
    driver: bridge
    internal: false  # Allow internet access for package installation

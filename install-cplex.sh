#!/bin/bash

# CPLEX Installation Script for VERRNN Docker Container
# This script handles the installation of IBM CPLEX Optimization Studio
# within the Docker container with support for manual intervention

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CPLEX_INSTALLER_TEMP="/tmp/cplex_installer.bin"
CPLEX_INSTALLER="/app/cplex/cplex_studio2210.linux_x86_64.bin"
CPLEX_INSTALL_DIR="/app/cplex/install"
CPLEX_PYTHON_DIR="$CPLEX_INSTALL_DIR/cplex/python"

echo -e "${BLUE}=== VERRNN CPLEX Installation Script ===${NC}"
echo -e "${BLUE}This script will install IBM CPLEX Optimization Studio${NC}"
echo -e "${BLUE}in the Docker container for the VERRNN project.${NC}"
echo ""

# Check if running inside Docker container
if [ ! -f /.dockerenv ]; then
    echo -e "${RED}Error: This script should be run inside the Docker container.${NC}"
    echo -e "${YELLOW}Please run: docker-compose exec verrnn bash${NC}"
    echo -e "${YELLOW}Then execute: ./install-cplex.sh${NC}"
    exit 1
fi

# Check if CPLEX installer exists (try both locations)
if [ -f "$CPLEX_INSTALLER_TEMP" ]; then
    echo -e "${GREEN}Found CPLEX installer at $CPLEX_INSTALLER_TEMP${NC}"
    # Copy to writable location and make executable
    cp "$CPLEX_INSTALLER_TEMP" "$CPLEX_INSTALLER"
    chmod +x "$CPLEX_INSTALLER"
elif [ -f "$CPLEX_INSTALLER" ]; then
    echo -e "${GREEN}Found CPLEX installer at $CPLEX_INSTALLER${NC}"
    chmod +x "$CPLEX_INSTALLER" 2>/dev/null || echo -e "${YELLOW}Note: Could not make installer executable (may be read-only mount)${NC}"
else
    echo -e "${RED}Error: CPLEX installer not found${NC}"
    echo -e "${YELLOW}Please ensure the cplex_studio2210.linux_x86_64.bin file is available${NC}"
    echo -e "${YELLOW}and mounted in the Docker container.${NC}"
    exit 1
fi

# Check if CPLEX is already installed
if [ -d "$CPLEX_INSTALL_DIR/cplex" ]; then
    echo -e "${YELLOW}CPLEX appears to be already installed.${NC}"
    read -p "Do you want to reinstall? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${GREEN}Skipping installation. Proceeding to Python API setup.${NC}"
        skip_install=true
    else
        echo -e "${YELLOW}Removing existing installation...${NC}"
        rm -rf "$CPLEX_INSTALL_DIR"/*
        skip_install=false
    fi
else
    skip_install=false
fi

# Create installation directory
mkdir -p "$CPLEX_INSTALL_DIR"

if [ "$skip_install" = false ]; then
    echo -e "${BLUE}Starting CPLEX installation...${NC}"
    echo -e "${YELLOW}Note: You will need to interact with the installer manually.${NC}"
    echo -e "${YELLOW}Please follow these guidelines:${NC}"
    echo -e "${YELLOW}1. Accept the license agreement${NC}"
    echo -e "${YELLOW}2. Choose installation directory: $CPLEX_INSTALL_DIR${NC}"
    echo -e "${YELLOW}3. Select all components (CPLEX, CP Optimizer, etc.)${NC}"
    echo -e "${YELLOW}4. Complete the installation${NC}"
    echo ""
    
    read -p "Press Enter to start the CPLEX installer..." -r
    
    # Make installer executable and run it
    chmod +x "$CPLEX_INSTALLER"
    
    echo -e "${BLUE}Running CPLEX installer...${NC}"
    "$CPLEX_INSTALLER" -i console -DUSER_INSTALL_DIR="$CPLEX_INSTALL_DIR"
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}CPLEX installation failed!${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}CPLEX installation completed successfully!${NC}"
fi

# Install CPLEX Python API
echo -e "${BLUE}Installing CPLEX Python API...${NC}"

# Find the CPLEX Python directory
CPLEX_PYTHON_SETUP=""
if [ -d "$CPLEX_PYTHON_DIR" ]; then
    # Look for setup.py in subdirectories
    for version_dir in "$CPLEX_PYTHON_DIR"/*; do
        if [ -d "$version_dir" ] && [ -f "$version_dir/setup.py" ]; then
            CPLEX_PYTHON_SETUP="$version_dir"
            break
        fi
    done
fi

if [ -z "$CPLEX_PYTHON_SETUP" ]; then
    echo -e "${RED}Error: Could not find CPLEX Python API setup.py${NC}"
    echo -e "${YELLOW}Please check the CPLEX installation directory structure.${NC}"
    exit 1
fi

echo -e "${BLUE}Found CPLEX Python API at: $CPLEX_PYTHON_SETUP${NC}"

# Install the Python API
cd "$CPLEX_PYTHON_SETUP"
python3 setup.py install --user

if [ $? -eq 0 ]; then
    echo -e "${GREEN}CPLEX Python API installed successfully!${NC}"
else
    echo -e "${RED}Failed to install CPLEX Python API!${NC}"
    exit 1
fi

# Test CPLEX installation
echo -e "${BLUE}Testing CPLEX installation...${NC}"
cd /app/verrnn

python3 -c "
try:
    import cplex
    print('✓ CPLEX Python API imported successfully')
    
    # Test basic functionality
    prob = cplex.Cplex()
    print('✓ CPLEX solver instance created successfully')
    print('CPLEX version:', prob.get_version())
    
except ImportError as e:
    print('✗ Failed to import CPLEX:', e)
    exit(1)
except Exception as e:
    print('✗ Error testing CPLEX:', e)
    exit(1)
"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}=== CPLEX Installation Complete! ===${NC}"
    echo -e "${GREEN}CPLEX is now ready for use with VERRNN.${NC}"
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo -e "${YELLOW}1. Update Qhull path in the project configuration${NC}"
    echo -e "${YELLOW}2. Run VERRNN tests to verify everything works${NC}"
    echo -e "${YELLOW}3. Use: python3 pp_test.py, python3 fp_test.py, etc.${NC}"
else
    echo -e "${RED}CPLEX installation test failed!${NC}"
    exit 1
fi

#!/bin/bash
# VERRNN Test Runner

echo "=== VERRNN Test Runner ==="
echo "Available tests:"
echo "1. Polytope Propagation Test (pp_test.py)"
echo "2. Fixed Point Test (fp_test.py)" 
echo "3. CEGAR Test (cegar_test.py)"
echo "4. Pulse Test (pulse_test.py)"
echo ""

cd /app/verrnn

case "$1" in
    "pp"|"1")
        echo "Running Polytope Propagation Test..."
        python3 pp_test.py
        ;;
    "fp"|"2")
        echo "Running Fixed Point Test..."
        python3 fp_test.py
        ;;
    "cegar"|"3")
        echo "Running CEGAR Test..."
        python3 cegar_test.py
        ;;
    "pulse"|"4")
        echo "Running Pulse Test..."
        python3 pulse_test.py
        ;;
    *)
        echo "Usage: $0 [pp|fp|cegar|pulse]"
        echo "   or: $0 [1|2|3|4]"
        exit 1
        ;;
esac
